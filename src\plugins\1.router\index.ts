import { setupLayouts } from 'virtual:generated-layouts'
import type { App } from 'vue'

import type { RouteRecordRaw } from 'vue-router/auto'

import { createRouter, createWebHistory } from 'vue-router/auto'

// 新增：全局路由守卫，设置网页标题
import { getI18n } from '@/plugins/i18n'

function recursiveLayouts(route: RouteRecordRaw): RouteRecordRaw {
  if (route.children) {
    for (let i = 0; i < route.children.length; i++)
      route.children[i] = recursiveLayouts(route.children[i])

    return route
  }

  return setupLayouts([route])[0]
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to) {
    if (to.hash)
      return { el: to.hash, behavior: 'smooth', top: 60 }

    return { top: 0 }
  },
  extendRoutes: pages => [
    ...[...pages].map(route => recursiveLayouts(route)),
  ],
})

// 路由前置守卫：在跳转到 login 页面之前清空验证码
router.beforeEach((to, _from, next) => {
  // 如果目标路由是 login 页面，清空 sessionStorage 中的 captcha
  if (to.path === '/login' || to.name === 'login')
    sessionStorage.setItem('captcha', '')

  next()
})

router.afterEach(() => {
  const i18n = getI18n()

  const titleKey = `${i18n.global.t('IoT')} . ${i18n.global.t('CommercialNetwork')}`

  document.title = titleKey
})

export { router }

export default function (app: App) {
  app.use(router)
}
