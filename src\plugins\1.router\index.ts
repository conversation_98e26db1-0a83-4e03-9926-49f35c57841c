import { setupLayouts } from 'virtual:generated-layouts'
import type { App } from 'vue'

import type { RouteRecordRaw } from 'vue-router/auto'

import { createRouter, createWebHistory } from 'vue-router/auto'

// 新增：全局路由守卫，设置网页标题
import { useNotificationsStore } from '@/@core/stores/notifications'
import { getI18n } from '@/plugins/i18n'

function recursiveLayouts(route: RouteRecordRaw): RouteRecordRaw {
  if (route.children) {
    for (let i = 0; i < route.children.length; i++)
      route.children[i] = recursiveLayouts(route.children[i])

    return route
  }

  return setupLayouts([route])[0]
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to) {
    if (to.hash)
      return { el: to.hash, behavior: 'smooth', top: 60 }

    return { top: 0 }
  },
  extendRoutes: pages => [
    ...[...pages].map(route => recursiveLayouts(route)),
  ],
})

// 路由前置守卫：在跳转到 login 页面之前清空验证码
router.beforeEach((to, from, next) => {
  // 如果目标路由是 login 页面，清空 sessionStorage 中的 captcha 并停止事件列表自动更新
  if (to.path === '/login' || to.name === 'login') {
    sessionStorage.setItem('captcha', '')

    // 停止事件列表的自动更新
    try {
      const notificationsStore = useNotificationsStore()

      notificationsStore.stopAutoUpdate()
    }
    catch (error) {
      // 如果 store 还未初始化，忽略错误
      console.log('Notifications store not yet initialized')
    }
  }

  next()
})

router.afterEach((to, from) => {
  const i18n = getI18n()

  const titleKey = `${i18n.global.t('IoT')} . ${i18n.global.t('CommercialNetwork')}`

  document.title = titleKey

  // 如果从登录页面跳转到其他页面，且有 captcha，则重新启动事件列表自动更新
  if ((from.path === '/login' || from.name === 'login')
      && (to.path !== '/login' && to.name !== 'login')
      && sessionStorage.getItem('captcha')) {
    try {
      const notificationsStore = useNotificationsStore()

      notificationsStore.startAutoUpdate()
    }
    catch (error) {
      // 如果 store 还未初始化，忽略错误
      console.log('Notifications store not yet initialized')
    }
  }
})

export { router }

export default function (app: App) {
  app.use(router)
}
