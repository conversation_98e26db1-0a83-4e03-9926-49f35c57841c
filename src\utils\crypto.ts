/**
 * AES-256-CBC 加密解密工具函数
 * 基于crypto-js实现，兼容C代码的OpenSSL实现
 */

import CryptoJS from 'crypto-js'

// 固定密钥 (32字节，转换为hex字符串)
const ENCRYPTION_KEY_HEX = '7d1a9fe432b80c55a72ed963f04b81ce269d703fbb14e8c25907ad934ef568dc'

// 加密结果类型定义
export interface EncryptionResult {
  [key: string]: string
}

/**
 * AES-256-CBC 加密
 * @param plaintext 明文字符串
 * @param prefix 前缀标识符，默认为 "key"
 * @returns 包含完整加密数据的对象
 */
export function encryptPassword(plaintext: string, prefix: string = 'key'): EncryptionResult {
  try {
    // 生成随机IV
    const iv = CryptoJS.lib.WordArray.random(16)
    const key = CryptoJS.enc.Hex.parse(ENCRYPTION_KEY_HEX)

    // AES-256-CBC加密
    const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })

    const ciphertextBase64 = encrypted.toString()

    // 计算HMAC
    const ciphertextWordArray = CryptoJS.enc.Base64.parse(ciphertextBase64)
    const hmac = CryptoJS.HmacSHA256(ciphertextWordArray, key)

    // 构建返回对象，包含四个值
    const result: EncryptionResult = {
      [prefix]: '0x55AA55', // 固定标识符
      [`${prefix}_0`]: iv.toString(CryptoJS.enc.Base64),
      [`${prefix}_1`]: ciphertextBase64,
      [`${prefix}_2`]: hmac.toString(CryptoJS.enc.Base64),
    }

    return result
  }
  catch (error) {
    console.error('Encryption failed:', error)
    throw new Error('Encryption failed')
  }
}

/**
 * AES-256-CBC 解密
 * @param ivBase64 Base64编码的IV
 * @param ciphertextBase64 Base64编码的密文
 * @param hmacBase64 Base64编码的HMAC
 * @returns 解密后的明文字符串
 */
export function decryptPassword(
  ivBase64: string,
  ciphertextBase64: string,
  hmacBase64: string
): string {
  try {
    const key = CryptoJS.enc.Hex.parse(ENCRYPTION_KEY_HEX)
    
    // 验证HMAC
    const ciphertextWordArray = CryptoJS.enc.Base64.parse(ciphertextBase64)
    const calculatedHmac = CryptoJS.HmacSHA256(ciphertextWordArray, key)
    const calculatedHmacBase64 = calculatedHmac.toString(CryptoJS.enc.Base64)
    
    if (calculatedHmacBase64 !== hmacBase64) {
      throw new Error('HMAC verification failed')
    }
    
    // 解密
    const iv = CryptoJS.enc.Base64.parse(ivBase64)
    const decrypted = CryptoJS.AES.decrypt(ciphertextBase64, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    
    return decrypted.toString(CryptoJS.enc.Utf8)
  }
  catch (error) {
    console.error('Decryption failed:', error)
    throw new Error('Decryption failed')
  }
}

/**
 * 通用加密数据解密函数
 * @param encryptedData 加密数据对象，支持多种格式
 * @returns 解密后的明文
 */
export function decryptEncryptedData(encryptedData: 
  | { key?: string; key_0: string; key_1: string; key_2: string }
  | { usr_psw?: string; usr_psw_0: string; usr_psw_1: string; usr_psw_2: string }
  | { [prefix: string]: string }
): string {
  // 自动检测数据格式并提取IV、密文、HMAC
  let iv: string, ciphertext: string, hmac: string

  // 检查用户密码格式
  if ('usr_psw_0' in encryptedData && 'usr_psw_1' in encryptedData && 'usr_psw_2' in encryptedData) {
    iv = encryptedData.usr_psw_0
    ciphertext = encryptedData.usr_psw_1
    hmac = encryptedData.usr_psw_2
  }
  // 检查密钥格式
  else if ('key_0' in encryptedData && 'key_1' in encryptedData && 'key_2' in encryptedData) {
    iv = encryptedData.key_0
    ciphertext = encryptedData.key_1
    hmac = encryptedData.key_2
  }
  // 通用格式检测：查找 _0, _1, _2 后缀的字段
  else {
    const keys = Object.keys(encryptedData)
    const suffixPattern = /^(.+)_([012])$/
    const groups: Record<string, Record<string, string>> = {}
    
    // 按前缀分组
    for (const key of keys) {
      const match = key.match(suffixPattern)
      if (match) {
        const [, prefix, suffix] = match
        if (!groups[prefix]) groups[prefix] = {}
        groups[prefix][suffix] = encryptedData[key]
      }
    }
    
    // 找到第一个完整的加密数据组
    const completeGroup = Object.entries(groups).find(([, group]) => 
      group['0'] && group['1'] && group['2']
    )
    
    if (!completeGroup) {
      throw new Error('无法识别加密数据格式，需要包含 _0(IV)、_1(密文)、_2(HMAC) 字段')
    }
    
    const [, group] = completeGroup
    iv = group['0']
    ciphertext = group['1'] 
    hmac = group['2']
  }

  return decryptPassword(iv, ciphertext, hmac)
}
