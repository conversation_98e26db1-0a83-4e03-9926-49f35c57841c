import { defineStore } from 'pinia'

export interface NotificationEvent {
  event_id: number
  event_name: number
  severity: string
  timestamp: string
  isRead: number
  status: number
  mac?: string
  ip?: string
  device_type?: string
  user_name?: string
  [key: string]: any
}

export const useNotificationsStore = defineStore('notifications', () => {
  // 状态
  const notifications = ref<NotificationEvent[]>([])
  const isLoading = ref(false)
  const lastUpdateTime = ref<Date | null>(null)
  const updateTimer = ref<NodeJS.Timeout | null>(null)

  // 配置
  const UPDATE_INTERVAL = 30 * 1000 // 30秒

  // 获取事件列表
  const fetchEventList = async () => {
    const captcha = sessionStorage.getItem('captcha')
    if (!captcha)
      return

    try {
      isLoading.value = true

      const result = await $api('', { requestType: 522 })

      if (result.err_code === 0) {
        notifications.value = result.info.ap_events || []
        lastUpdateTime.value = new Date()
      }
      else {
        console.error('获取事件列表失败:', result.err_msg)
      }
    }
    catch (error) {
      console.error('获取事件列表异常:', error)
    }
    finally {
      isLoading.value = false
    }
  }

  // 标记事件为已读
  const markEventAsRead = async (eventId: number) => {
    try {
      const result = await $api('', {
        requestType: 538,
        data: {
          event_id: eventId,
          isRead: '1',
        },
      })

      if (result.err_code === 0) {
        // 更新本地状态
        const event = notifications.value.find(n => n.event_id === eventId)
        if (event)
          event.isRead = 1

        // 重新获取最新数据
        await fetchEventList()
      }

      return result.err_code === 0
    }
    catch (error) {
      console.error('标记已读失败:', error)

      return false
    }
  }

  // 忽略事件
  const ignoreEvent = async (eventId: number) => {
    try {
      const result = await $api('', {
        requestType: 524,
        data: {
          event_id: eventId,
          status: '1',
        },
      })

      if (result.err_code === 0) {
        // 更新本地状态
        const event = notifications.value.find(n => n.event_id === eventId)
        if (event)
          event.status = 1

        // 重新获取最新数据
        await fetchEventList()
      }

      return result.err_code === 0
    }
    catch (error) {
      console.error('忽略事件失败:', error)

      return false
    }
  }

  // 停止定时更新
  const stopAutoUpdate = () => {
    if (updateTimer.value) {
      clearInterval(updateTimer.value)
      updateTimer.value = null
      console.log('事件列表自动更新已停止')
    }
  }

  // 启动定时更新
  const startAutoUpdate = () => {
    // 如果已经有定时器在运行，直接返回
    if (updateTimer.value)
      return

    const captcha = sessionStorage.getItem('captcha')
    if (!captcha)
      return

    // 立即获取一次数据
    fetchEventList()

    // 设置定时器
    updateTimer.value = setInterval(() => {
      fetchEventList()
    }, UPDATE_INTERVAL)
  }

  // 计算属性
  const unreadCount = computed(() => {
    return notifications.value.filter(n => n.isRead === 0).length
  })

  const hasUnreadNotifications = computed(() => {
    return unreadCount.value > 0
  })

  // 按严重程度过滤
  const getNotificationsBySeverity = (severity?: string) => {
    if (!severity)
      return notifications.value

    return notifications.value.filter(n => n.severity === severity)
  }

  // 获取未读通知
  const unreadNotifications = computed(() => {
    return notifications.value.filter(n => n.isRead === 0)
  })

  // 获取已读通知
  const readNotifications = computed(() => {
    return notifications.value.filter(n => n.isRead === 1)
  })

  // 一键已读
  const markAllAsRead = async () => {
    const result = await $api('', {
      requestType: 545,
      data: {
        isRead: '1',
      },
    })

    if (result.err_code === 0) {
      // 重新获取最新数据
      await fetchEventList()
    }
  }

  return {
    // 状态
    notifications: readonly(notifications),
    isLoading: readonly(isLoading),
    lastUpdateTime: readonly(lastUpdateTime),

    // 计算属性
    unreadCount,
    hasUnreadNotifications,
    unreadNotifications,
    readNotifications,

    // 方法
    fetchEventList,
    markEventAsRead,
    ignoreEvent,
    startAutoUpdate,
    stopAutoUpdate,
    getNotificationsBySeverity,
    markAllAsRead,
  }
})
