# 加密解密工具函数使用说明

## 概述

提供与C代码兼容的AES-256-CBC加密解密功能，基于crypto-js实现，支持HMAC-SHA256完整性验证。
支持所有浏览器环境，无需Web Crypto API。

## 核心函数

### 1. 加密函数

```typescript
import { encryptPassword } from '@/utils/crypto'

// 使用默认前缀 "key" 加密
const result = encryptPassword('myPassword123')
console.log(result)
// 输出:
// {
//   key: "0x55AA55",
//   key_0: "base64编码的IV",
//   key_1: "base64编码的密文", 
//   key_2: "base64编码的HMAC"
// }

// 使用自定义前缀加密
const userResult = encryptPassword('userPassword', 'usr_psw')
console.log(userResult)
// 输出:
// {
//   usr_psw: "0x55AA55",
//   usr_psw_0: "base64编码的IV",
//   usr_psw_1: "base64编码的密文", 
//   usr_psw_2: "base64编码的HMAC"
// }
```

### 2. 解密函数

```typescript
import { decryptEncryptedData } from '@/utils/crypto'

// 解密用户密码格式
const passwordData = {
  usr_psw: "0x55AA55",
  usr_psw_0: "7bQlx5PzQCM8bhpvwLd/Ew==",
  usr_psw_1: "CQCLeVDJ4NGjAMwHjs4aYg==", 
  usr_psw_2: "Z9SzldQau+pawW7a8m2LG26Lm14dyaMeYhAcIItOmhE="
}
const password = decryptEncryptedData(passwordData)
console.log(password) // "50751063"

// 解密密钥格式
const keyData = {
  key: "0x55AA55",
  key_0: "Ar71G6CcaHnPN9+lrwcy9Q==",
  key_1: "1LHY+ZlVF31K6X5fkEk7jQ==",
  key_2: "CLrJHLgy7vD5ryWRpeRvt21FxNGlV6652y9PHHtkLs8="
}
const key = decryptEncryptedData(keyData)
console.log(key) // "77777777"
```

## 使用示例

```typescript
import { encryptPassword, decryptEncryptedData } from '@/utils/crypto'

// 完整的加密解密流程
const plaintext = 'mySecretPassword'
const encrypted = encryptPassword(plaintext, 'usr_psw')
const decrypted = decryptEncryptedData(encrypted)
console.log(decrypted === plaintext) // true
```

## 测试

在浏览器控制台中运行：

```javascript
// 导入并运行测试
import { runCryptoTests } from '@/utils/crypto-test'
await runCryptoTests()
```

## 技术细节

- **加密算法**: AES-256-CBC
- **密钥长度**: 256位 (32字节)
- **IV长度**: 128位 (16字节)
- **HMAC算法**: HMAC-SHA256
- **编码格式**: Base64
- **依赖**: crypto-js

## 安全注意事项

1. 密钥是硬编码的，仅用于特定的应用场景
2. 每次加密都会生成随机IV，确保相同明文产生不同密文
3. 使用HMAC进行完整性验证，防止密文被篡改
4. 解密时会验证HMAC，确保数据完整性

## 错误处理

所有函数都会抛出错误，建议使用try-catch进行错误处理：

```typescript
try {
  const result = decryptEncryptedData(encryptedData)
  console.log('解密成功:', result)
} catch (error) {
  console.error('解密失败:', error.message)
}
```
